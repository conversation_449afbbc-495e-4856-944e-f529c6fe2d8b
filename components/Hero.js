import ButtonSignin from "./ButtonSignin";
import OpenDocumentation from "./buttons/OpenDocumentation";

const Hero = () => {

  const openDocs = () => {
    window.open('https://data.stalkapi.com/docs', '_blank')
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-white via-blue-50 to-purple-100 dark:from-slate-900 dark:via-purple-900 dark:to-slate-900 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 dark:from-purple-500/20 dark:to-blue-500/20"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      {/* Animated Background Orbs */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 dark:bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 dark:bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>

      <div className="relative max-w-7xl mx-auto px-8 py-20 lg:py-32">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-16">

          {/* Left Content */}
          <div className="flex-1 text-center lg:text-left">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 border border-purple-500/30 rounded-full text-purple-600 dark:text-purple-300 text-sm font-medium mb-8">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              API Status: Online
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Connect to
              <span className="block bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-400 dark:to-blue-400 bg-clip-text text-transparent">
                StalkChain
              </span>
              Using StalkApi
            </h1>

            {/* Subheading */}
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl">
              {/* Build, scale, and manage your API integrations with StalkApi. Use the tools you're familiar with that are inside StalkChain for onchain Alpha. */}
              Build, scale, and manage your API integrations with StalkApi. Leverage familiar tools within StalkChain to access on-chain Alpha insights with StalkApi.
              {/* Create powerful API integrations with StalkApi. Access on-chain alpha using the familiar tools already built into StalkChain. */}
              {/* Streamline your API development with StalkApi. Harness StalkChain's integrated tools to unlock valuable on-chain alpha opportunities. */}
              {/* Build and scale API integrations with StalkApi. Access on-chain alpha through StalkChain's familiar, integrated toolset. */}
              {/* Develop robust API integrations using StalkApi. Tap into on-chain alpha with StalkChain's developer-friendly tools you already know. */}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <ButtonSignin
                text="Start Building"
                extraStyle="btn-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 border-0 text-white px-8"
              />
              <OpenDocumentation />
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 text-center lg:text-left">
              <div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white">99.9%</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">Uptime SLA</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white">1M+</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">API Calls/Day</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white">50ms</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">Avg Response</div>
              </div>
            </div>
          </div>

          {/* Right Content - API Demo */}
          <div className="flex-1 max-w-2xl">
            <div className="relative">
              {/* Terminal Window */}
              <div className="bg-gray-100/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-300 dark:border-gray-700 shadow-2xl">
                {/* Terminal Header */}
                <div className="flex items-center px-4 py-3 border-b border-gray-300 dark:border-gray-700">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="flex-1 text-center text-gray-600 dark:text-gray-400 text-sm font-mono">
                    StalkAPI Terminal
                  </div>
                </div>

                {/* Terminal Content */}
                <div className="p-6 font-mono text-sm">
                  <div className="space-y-4">
                    {/* API Request */}
                    <div>
                      <span className="text-green-500 dark:text-green-400">$</span>
                      <span className="text-gray-800 dark:text-white ml-2">curl -X GET &quot;https://api.stalkapi.com/v1/data&quot;</span>
                    </div>
                    <div>
                      <span className="text-green-500 dark:text-green-400 ml-4">-H</span>
                      <span className="text-blue-600 dark:text-blue-400 ml-2">&quot;X-API-Key: your_api_key&quot;</span>
                    </div>

                    {/* Response */}
                    <div className="mt-4 text-gray-700 dark:text-gray-300">
                      <div className="text-yellow-600 dark:text-yellow-400">HTTP/1.1 200 OK</div>
                      <div className="text-purple-600 dark:text-purple-400">X-Credits-Remaining: 9,998</div>
                      <div className="text-purple-600 dark:text-purple-400">X-RateLimit-Remaining: 299</div>
                      <div className="mt-2">
                        <span className="text-gray-800 dark:text-white">{"{"}</span>
                        <div className="ml-4">
                          <div><span className="text-blue-600 dark:text-blue-400">&quot;success&quot;</span>: <span className="text-green-600 dark:text-green-400">true</span>,</div>
                          <div><span className="text-blue-600 dark:text-blue-400">&quot;data&quot;</span>: {"{"}</div>
                          <div className="ml-4">
                            <div><span className="text-blue-600 dark:text-blue-400">&quot;id&quot;</span>: <span className="text-yellow-600 dark:text-yellow-400">&quot;api_123&quot;</span>,</div>
                            <div><span className="text-blue-600 dark:text-blue-400">&quot;timestamp&quot;</span>: <span className="text-yellow-600 dark:text-yellow-400">&quot;2024-01-15T10:30:00Z&quot;</span>,</div>
                            <div><span className="text-blue-600 dark:text-blue-400">&quot;status&quot;</span>: <span className="text-yellow-600 dark:text-yellow-400">&quot;active&quot;</span></div>
                          </div>
                          <div>{"}"},</div>
                          <div><span className="text-blue-600 dark:text-blue-400">&quot;credits_used&quot;</span>: <span className="text-green-600 dark:text-green-400">2</span></div>
                        </div>
                        <span className="text-gray-800 dark:text-white">{"}"}</span>
                      </div>
                    </div>

                    {/* Cursor */}
                    <div className="flex items-center">
                      <span className="text-green-500 dark:text-green-400">$</span>
                      <span className="ml-2 w-2 h-5 bg-gray-800 dark:bg-white animate-pulse"></span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold animate-bounce">
                Live API
              </div>
              <div className="absolute -bottom-4 -left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                Real-time
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
