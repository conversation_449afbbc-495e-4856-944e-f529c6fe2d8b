"use client";

import { useState, useEffect } from "react";

const WebSocketStatus = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionCount, setConnectionCount] = useState(0);
  
  // Mock WebSocket connections data
  const connections = [
    {
      id: 1,
      stream: "kol-feed",
      status: "connected",
      connectedAt: "2024-01-15T10:30:00Z",
      messagesReceived: 1247
    },
    {
      id: 2,
      stream: "market-data",
      status: "connected",
      connectedAt: "2024-01-15T09:15:00Z",
      messagesReceived: 856
    },
    {
      id: 3,
      stream: "alerts",
      status: "disconnected",
      connectedAt: "2024-01-15T08:45:00Z",
      messagesReceived: 234
    }
  ];

  const activeConnections = connections.filter(conn => conn.status === "connected");

  // Simulate connection status updates
  useEffect(() => {
    const interval = setInterval(() => {
      setIsConnected(activeConnections.length > 0);
      setConnectionCount(activeConnections.length);
    }, 1000);

    return () => clearInterval(interval);
  }, [activeConnections.length]);

  const getStatusColor = (status) => {
    switch (status) {
      case "connected":
        return "text-green-600 dark:text-green-400";
      case "disconnected":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  const getStatusBg = (status) => {
    switch (status) {
      case "connected":
        return "bg-green-100 dark:bg-green-900/30 border-green-200 dark:border-green-800";
      case "disconnected":
        return "bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800";
      default:
        return "bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600";
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          WebSocket Status
        </h3>
        <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
          </svg>
        </div>
      </div>

      {/* Overall Status */}
      <div className={`p-4 rounded-lg border mb-6 ${
        isConnected 
          ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
          : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              isConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
            }`}></div>
            <div>
              <p className={`font-medium ${
                isConnected 
                  ? "text-green-800 dark:text-green-200"
                  : "text-red-800 dark:text-red-200"
              }`}>
                {isConnected ? "Connected" : "Disconnected"}
              </p>
              <p className={`text-sm ${
                isConnected 
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}>
                {connectionCount} active connection{connectionCount !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600 dark:text-gray-400">Limit</p>
            <p className="font-medium text-gray-900 dark:text-white">
              {connectionCount}/10
            </p>
          </div>
        </div>
      </div>

      {/* Connection List */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Active Streams
        </h4>
        
        {connections.map((connection) => (
          <div key={connection.id} className={`p-3 rounded-lg border ${getStatusBg(connection.status)}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-900 dark:text-white text-sm">
                  {connection.stream}
                </span>
                <span className={`text-xs font-medium ${getStatusColor(connection.status)}`}>
                  {connection.status}
                </span>
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formatTime(connection.connectedAt)}
              </span>
            </div>
            
            <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
              <span>Messages: {connection.messagesReceived.toLocaleString()}</span>
              {connection.status === "connected" && (
                <span className="text-green-600 dark:text-green-400">● Live</span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="w-full flex items-center justify-center px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white rounded-lg transition-all duration-200 text-sm font-medium">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          New Connection
        </button>
      </div>
    </div>
  );
};

export default WebSocketStatus;
