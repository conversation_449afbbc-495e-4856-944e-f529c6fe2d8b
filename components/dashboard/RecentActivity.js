"use client";

import { useState } from "react";

const RecentActivity = () => {
  const [filter, setFilter] = useState("all");
  
  // Mock activity data - in real implementation, this would come from your API
  const activities = [
    {
      id: 1,
      type: "api_call",
      endpoint: "/api/v1/kol-feed/history",
      method: "GET",
      status: "success",
      credits: 3,
      timestamp: "2024-01-15T14:30:00Z",
      responseTime: "45ms",
      ip: "*************"
    },
    {
      id: 2,
      type: "websocket",
      endpoint: "kol-feed stream",
      method: "CONNECT",
      status: "success",
      credits: 0,
      timestamp: "2024-01-15T14:25:00Z",
      responseTime: "12ms",
      ip: "*************"
    },
    {
      id: 3,
      type: "api_call",
      endpoint: "/api/v1/analytics",
      method: "GET",
      status: "error",
      credits: 0,
      timestamp: "2024-01-15T14:20:00Z",
      responseTime: "timeout",
      ip: "*************",
      error: "Insufficient credits"
    },
    {
      id: 4,
      type: "api_call",
      endpoint: "/api/v1/data",
      method: "GET",
      status: "success",
      credits: 2,
      timestamp: "2024-01-15T14:15:00Z",
      responseTime: "38ms",
      ip: "*************"
    },
    {
      id: 5,
      type: "auth",
      endpoint: "API Key regenerated",
      method: "POST",
      status: "success",
      credits: 0,
      timestamp: "2024-01-15T13:45:00Z",
      responseTime: "-",
      ip: "*************"
    }
  ];

  const filteredActivities = activities.filter(activity => {
    if (filter === "all") return true;
    return activity.type === filter;
  });

  const getActivityIcon = (type, status) => {
    if (type === "api_call") {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      );
    } else if (type === "websocket") {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
      );
    } else if (type === "auth") {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.243-6.243A6 6 0 0121 9z" />
        </svg>
      );
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "success":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30";
      case "error":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700";
    }
  };

  const getMethodColor = (method) => {
    switch (method) {
      case "GET":
        return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30";
      case "POST":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30";
      case "PUT":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30";
      case "DELETE":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30";
      case "CONNECT":
        return "text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700";
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Activity
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Latest API calls and system events
          </p>
        </div>
        
        {/* Filter Dropdown */}
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value="all">All Activity</option>
          <option value="api_call">API Calls</option>
          <option value="websocket">WebSocket</option>
          <option value="auth">Authentication</option>
        </select>
      </div>

      {/* Activity List */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {filteredActivities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            {/* Icon */}
            <div className={`p-2 rounded-lg ${
              activity.status === "success" 
                ? "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400"
                : activity.status === "error"
                ? "bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
            }`}>
              {getActivityIcon(activity.type, activity.status)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span className={`px-2 py-1 text-xs font-medium rounded ${getMethodColor(activity.method)}`}>
                  {activity.method}
                </span>
                <span className={`px-2 py-1 text-xs font-medium rounded ${getStatusColor(activity.status)}`}>
                  {activity.status}
                </span>
                {activity.credits > 0 && (
                  <span className="px-2 py-1 text-xs font-medium rounded bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                    {activity.credits} credits
                  </span>
                )}
              </div>
              
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {activity.endpoint}
              </p>
              
              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-600 dark:text-gray-400">
                <span>{formatTime(activity.timestamp)}</span>
                <span>•</span>
                <span>{activity.responseTime}</span>
                <span>•</span>
                <span>{activity.ip}</span>
              </div>
              
              {activity.error && (
                <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                  Error: {activity.error}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* View All Button */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="w-full text-center text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 font-medium transition-colors">
          View All Activity →
        </button>
      </div>
    </div>
  );
};

export default RecentActivity;
